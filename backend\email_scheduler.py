"""
DEPRECATED: Email Scheduler Module for Email Analyzer

This module is DISABLED in favor of Pub/Sub event-driven architecture.
All scheduled processing functionality has been removed.
"""

import logging

logger = logging.getLogger("EmailScheduler")

def init_scheduler():
    """Disabled - using Pub/Sub event-driven architecture"""
    logger.warning("Email scheduler initialization skipped - using Pub/Sub event-driven architecture")
    return {"status": "disabled", "message": "Using Pub/Sub event-driven architecture"}

def get_scheduler_status():
    """Return disabled status"""
    return {"status": "disabled", "message": "Using Pub/Sub event-driven architecture"}

def fetch_and_analyze_emails(*args, **kwargs):
    """Disabled - using Pub/Sub event-driven architecture"""
    logger.warning("fetch_and_analyze_emails called but disabled - using Pub/Sub event-driven architecture")
    return {"status": "disabled", "message": "Using Pub/Sub event-driven architecture"}

def schedule_job(*args, **kwargs):
    """Disabled - using Pub/Sub event-driven architecture"""
    logger.warning("schedule_job called but disabled - using Pub/Sub event-driven architecture")
    return {"status": "disabled", "message": "Using Pub/Sub event-driven architecture"}

def remove_job(*args, **kwargs):
    """Disabled - using Pub/Sub event-driven architecture"""
    logger.warning("remove_job called but disabled - using Pub/Sub event-driven architecture")
    return {"status": "disabled", "message": "Using Pub/Sub event-driven architecture"}

def get_jobs(*args, **kwargs):
    """Disabled - using Pub/Sub event-driven architecture"""
    logger.warning("get_jobs called but disabled - using Pub/Sub event-driven architecture")
    return []

def run_job_now(*args, **kwargs):
    """Disabled - using Pub/Sub event-driven architecture"""
    logger.warning("run_job_now called but disabled - using Pub/Sub event-driven architecture")
    return {"status": "disabled", "message": "Using Pub/Sub event-driven architecture"}
