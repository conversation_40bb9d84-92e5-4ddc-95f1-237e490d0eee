"""
DEPRECATED: Cloud Email Trigger Module for Email Analyzer

This module is DISABLED in favor of Pub/Sub event-driven architecture.
All cloud trigger functionality has been removed.
"""

import logging
from fastapi import APIRouter

logger = logging.getLogger("CloudEmailTrigger")

# Disabled router
router = APIRouter()

@router.get("/disabled")
async def disabled_endpoint():
    """All cloud trigger endpoints are disabled"""
    return {"status": "disabled", "message": "Using Pub/Sub event-driven architecture"}

def enhanced_health_check():
    """Return disabled status"""
    return {"status": "disabled", "message": "Using Pub/Sub event-driven architecture"}

def start_auto_scheduler():
    """Disabled - using Pub/Sub event-driven architecture"""
    logger.warning("start_auto_scheduler called but disabled - using Pub/Sub event-driven architecture")
    return {"status": "disabled", "message": "Using Pub/Sub event-driven architecture"}

def stop_auto_scheduler():
    """Disabled - using Pub/Sub event-driven architecture"""
    logger.warning("stop_auto_scheduler called but disabled - using Pub/Sub event-driven architecture")
    return {"status": "disabled", "message": "Using Pub/Sub event-driven architecture"}

def process_emails_cloud_safe():
    """Disabled - using Pub/Sub event-driven architecture"""
    logger.warning("process_emails_cloud_safe called but disabled - using Pub/Sub event-driven architecture")
    return {"status": "disabled", "message": "Using Pub/Sub event-driven architecture"}
