# Email Analyzer - Pub/Sub Event-Driven Architecture Deployment Checklist

Use this checklist to ensure proper deployment of the **pure event-driven** email processing system.

**⚠️ IMPORTANT: This system NO LONGER uses scheduled processing. All email processing is triggered by Gmail push notifications via Pub/Sub.**

## 📋 Pre-Deployment Checklist

### ✅ Google Cloud Setup
- [ ] Google Cloud Project created with billing enabled
- [ ] Required APIs enabled:
  - [ ] Cloud Build API
  - [ ] Cloud Run API
  - [ ] Pub/Sub API
  - [ ] Firestore API
  - [ ] Gmail API
- [ ] Pub/Sub topic `email-notifications` created
- [ ] Service account `gmail-push-handler` created
- [ ] IAM permissions configured:
  - [ ] Gmail API push permissions
  - [ ] Pub/Sub subscriber role
  - [ ] Firestore user role

### ✅ Credentials and Configuration
- [ ] Firebase service account JSON file available
- [ ] Gmail API client secrets JSON file available
- [ ] Gemini AI API key obtained
- [ ] Environment variables configured in `.env` file
- [ ] Service token generated for Cloud Run ↔ FastAPI auth

### ✅ Code Preparation
- [ ] **Scheduled processing disabled**:
  - [ ] `email_scheduler.py` disabled (returns disabled status)
  - [ ] `background_processor.py` disabled (returns disabled status)
  - [ ] `cloud_email_trigger.py` disabled (returns disabled status)
  - [ ] `backend/main.py` updated for event-driven architecture only
- [ ] **Event-driven components ready**:
  - [ ] `pubsub_handler.py` (main Pub/Sub handler)
  - [ ] `backend/gmail_push_setup.py` (Gmail watch management)
  - [ ] `backend/routers/batch_email_router.py` with `/process-single` endpoint
- [ ] **Docker files ready**:
  - [ ] `Dockerfile.pubsub` for Pub/Sub handler
  - [ ] `requirements-pubsub.txt` optimized
  - [ ] Backend `requirements.txt` updated (apscheduler removed)
- [ ] **Unnecessary files removed**:
  - [ ] `scrap/` directory removed
  - [ ] Duplicate credential files removed

## 🚀 Deployment Steps

### Step 1: Deploy Cloud Run Handler
- [ ] Build Docker image:
  ```bash
  docker build -t gcr.io/YOUR_PROJECT_ID/gmail-push-handler .
  ```
- [ ] Push to Google Container Registry:
  ```bash
  docker push gcr.io/YOUR_PROJECT_ID/gmail-push-handler
  ```
- [ ] Deploy to Cloud Run:
  ```bash
  gcloud run deploy gmail-push-handler \
    --image gcr.io/YOUR_PROJECT_ID/gmail-push-handler \
    --platform managed \
    --region us-central1 \
    --allow-unauthenticated
  ```
- [ ] Note the Cloud Run service URL

### Step 2: Configure Pub/Sub Subscription
- [ ] Create subscription with push endpoint:
  ```bash
  gcloud pubsub subscriptions create gmail-notifications-subscription \
    --topic=email-notifications \
    --push-endpoint="CLOUD_RUN_URL/gmail-webhook"
  ```
- [ ] Verify subscription is active

### Step 3: Update FastAPI Backend
- [ ] Deploy updated FastAPI code with new endpoints
- [ ] Update environment variables with Cloud Run URL
- [ ] Verify FastAPI service is accessible

### Step 4: Initialize Gmail Watches
- [ ] Set up watches for existing users:
  ```bash
  curl -X POST "FASTAPI_URL/gmail/setup-watches" \
    -H "Authorization: Bearer USER_TOKEN"
  ```
- [ ] Verify watches are created successfully

## 🧪 Post-Deployment Testing

### Health Checks
- [ ] Cloud Run health check passes:
  ```bash
  curl https://CLOUD_RUN_URL/health
  ```
- [ ] FastAPI health check passes:
  ```bash
  curl https://FASTAPI_URL/
  ```

### Functional Testing
- [ ] Send test notification:
  ```bash
  python mock_pubsub_messages.py --url CLOUD_RUN_URL --mode test
  ```
- [ ] Verify email processing pipeline:
  - [ ] Notification received by Cloud Run
  - [ ] FastAPI endpoint called successfully
  - [ ] Email analysis completed
  - [ ] Webhook triggered (if applicable)

### Integration Testing
- [ ] Test Gmail watch setup endpoint
- [ ] Test watch status monitoring
- [ ] Test watch renewal functionality
- [ ] Verify real Gmail notifications work

## 📊 Monitoring Setup

### Alerts Configuration
- [ ] Set up Cloud Run error rate alerts
- [ ] Configure Pub/Sub backlog monitoring
- [ ] Set up Gmail watch expiration alerts
- [ ] Configure email processing latency alerts

### Logging Verification
- [ ] Cloud Run logs are accessible
- [ ] FastAPI logs include request tracing
- [ ] Error logs are properly formatted
- [ ] Audit logs are enabled for Firestore

### Metrics Dashboard
- [ ] Cloud Run metrics visible in console
- [ ] Pub/Sub metrics tracking
- [ ] Custom metrics for email processing
- [ ] Webhook delivery success rates

## 🔒 Security Verification

### Authentication
- [ ] Service-to-service authentication working
- [ ] User authentication still functional
- [ ] OAuth token refresh working
- [ ] Service account permissions minimal

### Data Protection
- [ ] HTTPS enforced for all endpoints
- [ ] Sensitive data encrypted at rest
- [ ] No credentials in logs
- [ ] Audit logging enabled

### Access Control
- [ ] IAM roles follow least privilege
- [ ] Service accounts have minimal permissions
- [ ] API endpoints properly secured
- [ ] Network security configured

## 🔄 Operational Readiness

### Documentation
- [ ] Deployment guide updated
- [ ] Troubleshooting documentation available
- [ ] Monitoring runbooks created
- [ ] Emergency procedures documented

### Backup and Recovery
- [ ] Firestore backup enabled
- [ ] Configuration backup available
- [ ] Rollback procedure tested
- [ ] Disaster recovery plan documented

### Maintenance Planning
- [ ] Watch renewal automation verified
- [ ] Credential rotation schedule set
- [ ] Update procedures documented
- [ ] Capacity planning completed

## ⚠️ Go-Live Checklist

### Final Verification
- [ ] All tests passing
- [ ] Performance meets requirements (<60s processing)
- [ ] Error rates within acceptable limits
- [ ] Monitoring and alerts active

### Communication
- [ ] Stakeholders notified of go-live
- [ ] Support team briefed
- [ ] Documentation shared
- [ ] Emergency contacts updated

### Rollback Plan
- [ ] Rollback procedure documented
- [ ] Previous version available
- [ ] Database migration rollback tested
- [ ] Emergency stop procedure ready

## 📈 Success Criteria

### Performance Targets
- [ ] Email processing latency < 60 seconds
- [ ] System availability > 99.9%
- [ ] Error rate < 1%
- [ ] Webhook delivery success > 95%

### Functional Requirements
- [ ] **Real-time email notifications working (Pub/Sub)**
- [ ] **NO scheduled processing running (disabled)**
- [ ] All email categories properly analyzed
- [ ] Purchase order webhooks triggered correctly
- [ ] Event-driven processing only (no polling)

### Operational Requirements
- [ ] Monitoring and alerting functional
- [ ] Logs accessible and searchable
- [ ] Automated watch renewal working
- [ ] Manual intervention procedures documented

---

## 🆘 Emergency Contacts

- **Primary Engineer**: [Your contact info]
- **Google Cloud Support**: [Support case link]
- **Firebase Support**: [Support case link]
- **On-call Engineer**: [Contact info]

## 📞 Escalation Procedure

1. **Level 1**: Check logs and basic troubleshooting
2. **Level 2**: Restart services and verify configuration
3. **Level 3**: Contact Google Cloud support
4. **Level 4**: Execute rollback procedure

---

**Deployment Date**: _______________
**Deployed By**: _______________
**Approved By**: _______________
**Next Review Date**: _______________
