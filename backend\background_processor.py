"""
DEPRECATED: Background Processor Module for Email Analyzer

This module is DISABLED in favor of Pub/Sub event-driven architecture.
All background processing functionality has been removed.
"""

import logging

logger = logging.getLogger("BackgroundProcessor")

def start_background_processor():
    """Disabled - using Pub/Sub event-driven architecture"""
    logger.warning("Background processor startup skipped - using Pub/Sub event-driven architecture")
    return {"status": "disabled", "message": "Using Pub/Sub event-driven architecture"}

def get_processor_status():
    """Return disabled status"""
    return {"status": "disabled", "message": "Using Pub/Sub event-driven architecture"}

def set_process_interval(*args, **kwargs):
    """Disabled - using Pub/Sub event-driven architecture"""
    logger.warning("set_process_interval called but disabled - using Pub/Sub event-driven architecture")
    return False

def reset_counters():
    """Disabled - using Pub/Sub event-driven architecture"""
    logger.warning("reset_counters called but disabled - using Pub/Sub event-driven architecture")
    return False

# Mock constants for compatibility
CURRENT_PROCESS_INTERVAL = 0
