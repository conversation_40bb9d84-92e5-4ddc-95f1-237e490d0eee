#!/usr/bin/env python3
"""
FastAPI Gmail Push Notification Handler

This FastAPI service handles Gmail push notifications from Google Cloud Pub/Sub,
processes them, and triggers email analysis via the main FastAPI backend.

Architecture:
1. Receives POST requests from Pub/Sub on /gmail-webhook
2. Parses base64-encoded messages containing Gmail notifications
3. Extracts emailAddress and historyId from notifications
4. Queries Firestore to find user by email address
5. Uses Gmail API to fetch new emails since historyId
6. Makes HTTP POST calls to main FastAPI /emails/process-single endpoint
"""

import os
import json
import base64
import logging
import traceback
from datetime import datetime
from typing import Dict, Any, Optional, List

from fastapi import FastAPI, HTTPException, Request, BackgroundTasks
from fastapi.responses import JSONResponse
from pydantic import BaseModel
import requests
from google.cloud import firestore
from google.oauth2.credentials import Credentials
from googleapiclient.discovery import build
from google.auth.transport.requests import Request as GoogleRequest

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger("gmail_push_handler")

# Configuration from environment variables
MAIN_FASTAPI_URL = os.getenv('MAIN_FASTAPI_URL', 'http://localhost:8000')
SERVICE_TOKEN = os.getenv('SERVICE_TOKEN', 'dev-service-token')
GOOGLE_CLOUD_PROJECT = os.getenv('GOOGLE_CLOUD_PROJECT', 'ai-email-bot-455814')
PUBSUB_TOPIC = os.getenv('PUBSUB_TOPIC', 'projects/ai-email-bot-455814/topics/email-notifications')
LOG_LEVEL = os.getenv('LOG_LEVEL', 'INFO')

# Performance and reliability settings
MAX_RETRIES = 3
RETRY_DELAY = 1  # seconds
REQUEST_TIMEOUT = 30  # seconds
MAX_EMAILS_PER_NOTIFICATION = 10  # Limit emails processed per notification

# Set log level
logger.setLevel(getattr(logging, LOG_LEVEL.upper()))

# Initialize FastAPI app
app = FastAPI(
    title="Gmail Push Notification Handler",
    description="FastAPI service for handling Gmail push notifications from Pub/Sub",
    version="1.0.0"
)

# Initialize Firestore client
try:
    db = firestore.Client()
    logger.info("Firestore client initialized successfully")
except Exception as e:
    logger.error(f"Failed to initialize Firestore client: {str(e)}")
    db = None

# Pydantic models
class PubSubMessage(BaseModel):
    """Pub/Sub message structure"""
    message: Dict[str, Any]
    subscription: Optional[str] = None

class GmailNotification(BaseModel):
    """Gmail notification data"""
    emailAddress: str
    historyId: str

class TestNotificationRequest(BaseModel):
    """Test notification request"""
    emailAddress: str
    historyId: str

class GmailNotificationProcessor:
    """Handles Gmail push notification processing"""
    
    def __init__(self):
        self.db = db
        self.main_fastapi_url = MAIN_FASTAPI_URL
        self.service_token = SERVICE_TOKEN
        
    def parse_pubsub_message(self, pubsub_data: Dict[str, Any]) -> Optional[GmailNotification]:
        """Parse Pub/Sub message and extract Gmail notification data"""
        try:
            # Extract message from Pub/Sub format
            message = pubsub_data.get('message', {})
            if not message:
                logger.error("No message found in Pub/Sub request")
                return None
                
            # Decode base64 data
            data = message.get('data', '')
            if not data:
                logger.error("No data found in Pub/Sub message")
                return None
                
            # Decode base64 message
            try:
                decoded_data = base64.b64decode(data).decode('utf-8')
                notification_data = json.loads(decoded_data)
                logger.info(f"Decoded notification data: {notification_data}")
                
                return GmailNotification(**notification_data)
            except Exception as decode_error:
                logger.error(f"Failed to decode Pub/Sub message: {str(decode_error)}")
                return None
                
        except Exception as e:
            logger.error(f"Error parsing Pub/Sub message: {str(e)}")
            return None
    
    def get_user_by_email(self, email_address: str) -> Optional[Dict[str, Any]]:
        """Find user in Firestore by email address in email_accounts collection"""
        try:
            if not self.db:
                logger.error("Firestore client not available")
                return None
                
            logger.info(f"Searching for user with email: {email_address}")
            
            # Query all users and their email accounts
            users_ref = self.db.collection('users')
            users = users_ref.stream()
            
            for user_doc in users:
                user_id = user_doc.id
                user_data = user_doc.to_dict()
                
                # Check email_accounts subcollection
                email_accounts_ref = users_ref.document(user_id).collection('email_accounts')
                accounts = email_accounts_ref.where('email', '==', email_address).stream()
                
                for account_doc in accounts:
                    account_data = account_doc.to_dict()
                    logger.info(f"Found user {user_id} with email account {account_doc.id}")
                    
                    return {
                        'user_id': user_id,
                        'user_data': user_data,
                        'account_id': account_doc.id,
                        'account_data': account_data,
                        'email_address': email_address
                    }
            
            logger.warning(f"No user found with email address: {email_address}")
            return None
            
        except Exception as e:
            logger.error(f"Error finding user by email: {str(e)}")
            return None

    def refresh_credentials_if_needed(self, credentials_dict: Dict[str, Any]) -> Optional[Credentials]:
        """Refresh Gmail API credentials if needed"""
        try:
            credentials = Credentials(
                token=credentials_dict.get('token'),
                refresh_token=credentials_dict.get('refresh_token'),
                token_uri=credentials_dict.get('token_uri'),
                client_id=credentials_dict.get('client_id'),
                client_secret=credentials_dict.get('client_secret'),
                scopes=credentials_dict.get('scopes', [])
            )

            # Check if credentials need refresh
            if not credentials.valid:
                if credentials.expired and credentials.refresh_token:
                    logger.info("Refreshing expired credentials")
                    credentials.refresh(GoogleRequest())
                    return credentials
                else:
                    logger.error("Credentials are invalid and cannot be refreshed")
                    return None

            return credentials

        except Exception as e:
            logger.error(f"Error refreshing credentials: {str(e)}")
            return None

    def fetch_new_emails(self, user_data: Dict[str, Any], history_id: str) -> List[Dict[str, Any]]:
        """Fetch new emails from Gmail API since the given history ID"""
        try:
            user_id = user_data['user_id']
            account_id = user_data['account_id']
            account_data = user_data['account_data']

            # Get credentials from account data
            credentials_dict = account_data.get('credentials', {})
            if not credentials_dict:
                logger.error(f"No credentials found for account {account_id}")
                return []

            # Refresh credentials if needed
            credentials = self.refresh_credentials_if_needed(credentials_dict)
            if not credentials:
                logger.error(f"Failed to get valid credentials for account {account_id}")
                return []

            # Build Gmail service
            service = build('gmail', 'v1', credentials=credentials)

            # Get history since the given history ID
            logger.info(f"Fetching history since {history_id} for user {user_id}")

            try:
                history_response = service.users().history().list(
                    userId='me',
                    startHistoryId=history_id,
                    historyTypes=['messageAdded'],
                    labelId='INBOX'
                ).execute()

                history_records = history_response.get('history', [])
                logger.info(f"Found {len(history_records)} history records")

                new_emails = []
                for record in history_records:
                    messages_added = record.get('messagesAdded', [])
                    for message_added in messages_added:
                        message = message_added.get('message', {})
                        email_id = message.get('id')

                        if email_id:
                            # Check if message is in INBOX
                            label_ids = message.get('labelIds', [])
                            if 'INBOX' in label_ids:
                                new_emails.append({
                                    'email_id': email_id,
                                    'user_id': user_id,
                                    'account_id': account_id,
                                    'thread_id': message.get('threadId'),
                                    'label_ids': label_ids
                                })
                                logger.info(f"Found new email: {email_id}")

                                # Limit the number of emails processed per notification
                                if len(new_emails) >= MAX_EMAILS_PER_NOTIFICATION:
                                    logger.info(f"Reached maximum emails per notification ({MAX_EMAILS_PER_NOTIFICATION}), stopping")
                                    break

                    # Break outer loop too if limit reached
                    if len(new_emails) >= MAX_EMAILS_PER_NOTIFICATION:
                        break

                logger.info(f"Total new emails found: {len(new_emails)}")
                return new_emails

            except Exception as history_error:
                logger.error(f"Error fetching Gmail history: {str(history_error)}")
                return []

        except Exception as e:
            logger.error(f"Error fetching new emails: {str(e)}")
            return []

    def trigger_email_analysis(self, email_id: str, user_id: str, account_id: str) -> bool:
        """Trigger email analysis via main FastAPI endpoint with retry logic"""
        import time

        for attempt in range(MAX_RETRIES):
            try:
                # Prepare request payload
                payload = {
                    'email_id': email_id,
                    'user_id': user_id,
                    'account_id': account_id,
                    'priority': 'real_time',
                    'source': 'gmail_push_notification',
                    'force_reanalysis': False
                }

                # Prepare headers with service token
                headers = {
                    'Content-Type': 'application/json',
                    'Authorization': f'Bearer {self.service_token}',
                    'X-Service-Source': 'pubsub-handler'
                }

                # Make request to main FastAPI
                url = f"{self.main_fastapi_url}/emails/process-single"
                logger.info(f"Triggering email analysis (attempt {attempt + 1}/{MAX_RETRIES}): {url}")
                logger.info(f"Payload: {payload}")

                response = requests.post(
                    url,
                    json=payload,
                    headers=headers,
                    timeout=REQUEST_TIMEOUT
                )

                if response.status_code == 200:
                    logger.info(f"Successfully triggered analysis for email {email_id}")
                    return True
                elif response.status_code in [500, 502, 503, 504] and attempt < MAX_RETRIES - 1:
                    # Retry on server errors
                    logger.warning(f"Server error {response.status_code}, retrying in {RETRY_DELAY} seconds...")
                    time.sleep(RETRY_DELAY)
                    continue
                else:
                    logger.error(f"Failed to trigger analysis. Status: {response.status_code}, Response: {response.text}")
                    return False

            except requests.exceptions.Timeout:
                if attempt < MAX_RETRIES - 1:
                    logger.warning(f"Request timeout, retrying in {RETRY_DELAY} seconds...")
                    time.sleep(RETRY_DELAY)
                    continue
                else:
                    logger.error(f"Request timeout after {MAX_RETRIES} attempts")
                    return False
            except Exception as e:
                if attempt < MAX_RETRIES - 1:
                    logger.warning(f"Error triggering email analysis (attempt {attempt + 1}): {str(e)}, retrying...")
                    time.sleep(RETRY_DELAY)
                    continue
                else:
                    logger.error(f"Error triggering email analysis after {MAX_RETRIES} attempts: {str(e)}")
                    return False

        return False

    async def process_gmail_notification(self, notification: GmailNotification) -> Dict[str, Any]:
        """Process individual Gmail notification"""
        try:
            logger.info(f"Processing notification for {notification.emailAddress}, historyId: {notification.historyId}")

            # Find user by email address
            user_data = self.get_user_by_email(notification.emailAddress)
            if not user_data:
                logger.warning(f"No user found for email address: {notification.emailAddress}")
                return {
                    'success': False,
                    'error': f'No user found for email address: {notification.emailAddress}'
                }

            # Fetch new emails since history ID
            new_emails = self.fetch_new_emails(user_data, notification.historyId)

            if not new_emails:
                logger.info(f"No new emails found for {notification.emailAddress}")
                return {
                    'success': True,
                    'message': 'No new emails to process',
                    'emails_processed': 0
                }

            # Trigger analysis for each new email
            successful_triggers = 0
            failed_triggers = 0

            for email_info in new_emails:
                success = self.trigger_email_analysis(
                    email_info['email_id'],
                    email_info['user_id'],
                    email_info['account_id']
                )

                if success:
                    successful_triggers += 1
                else:
                    failed_triggers += 1

            logger.info(f"Processed {len(new_emails)} emails. Success: {successful_triggers}, Failed: {failed_triggers}")

            return {
                'success': True,
                'message': f'Processed {len(new_emails)} emails',
                'emails_processed': len(new_emails),
                'successful_triggers': successful_triggers,
                'failed_triggers': failed_triggers
            }

        except Exception as e:
            logger.error(f"Error processing Gmail notification: {str(e)}")
            return {
                'success': False,
                'error': str(e)
            }

# Initialize the processor
processor = GmailNotificationProcessor()

# Simple metrics tracking
metrics = {
    'notifications_received': 0,
    'notifications_processed': 0,
    'notifications_failed': 0,
    'emails_triggered': 0,
    'emails_failed': 0,
    'last_notification_time': None,
    'service_start_time': datetime.now().isoformat()
}

# FastAPI endpoints
@app.get("/")
async def root():
    """Root endpoint"""
    return {
        "service": "Gmail Push Notification Handler",
        "version": "1.0.0",
        "status": "running",
        "framework": "FastAPI",
        "architecture": "event_driven_pubsub",
        "endpoints": {
            "health": "/health",
            "webhook": "/gmail-webhook",
            "test": "/test-notification",
            "metrics": "/metrics"
        }
    }

@app.get("/metrics")
async def get_metrics():
    """Get service metrics"""
    return {
        "service": "gmail-push-handler",
        "metrics": metrics,
        "uptime_seconds": (datetime.now() - datetime.fromisoformat(metrics['service_start_time'])).total_seconds()
    }

@app.get("/health")
async def health_check():
    """Health check endpoint"""
    try:
        health_status = {
            'status': 'healthy',
            'timestamp': datetime.now().isoformat(),
            'service': 'gmail-push-handler',
            'version': '1.0.0',
            'framework': 'FastAPI'
        }

        # Check Firestore connection
        if db:
            try:
                test_ref = db.collection('_health_check').document('test')
                test_ref.set({'timestamp': firestore.SERVER_TIMESTAMP})
                health_status['firestore'] = 'connected'
            except Exception as db_error:
                health_status['firestore'] = f'error: {str(db_error)}'
                health_status['status'] = 'degraded'
        else:
            health_status['firestore'] = 'not_initialized'
            health_status['status'] = 'degraded'

        # Check main FastAPI connectivity
        try:
            response = requests.get(f"{MAIN_FASTAPI_URL}/", timeout=5)
            if response.status_code == 200:
                health_status['main_fastapi'] = 'connected'
            else:
                health_status['main_fastapi'] = f'status_code: {response.status_code}'
                health_status['status'] = 'degraded'
        except Exception as api_error:
            health_status['main_fastapi'] = f'error: {str(api_error)}'
            health_status['status'] = 'degraded'

        status_code = 200 if health_status['status'] == 'healthy' else 503
        return JSONResponse(content=health_status, status_code=status_code)

    except Exception as e:
        logger.error(f"Health check failed: {str(e)}")
        return JSONResponse(
            content={
                'status': 'unhealthy',
                'error': str(e),
                'timestamp': datetime.now().isoformat()
            },
            status_code=503
        )

@app.post("/gmail-webhook")
async def handle_gmail_notification(request: Request, background_tasks: BackgroundTasks):
    """Handle Gmail push notifications from Pub/Sub"""
    try:
        # Update metrics
        metrics['notifications_received'] += 1
        metrics['last_notification_time'] = datetime.now().isoformat()

        logger.info("Received Gmail push notification")

        # Get request data
        request_data = await request.json()
        logger.info(f"Request data: {request_data}")

        # Parse Pub/Sub message
        notification = processor.parse_pubsub_message(request_data)
        if not notification:
            logger.error("Failed to parse Pub/Sub message")
            metrics['notifications_failed'] += 1
            raise HTTPException(status_code=400, detail="Failed to parse Pub/Sub message")

        # Process the notification in background
        background_tasks.add_task(
            process_notification_background,
            notification
        )

        # Return immediate response to Pub/Sub
        return {
            "success": True,
            "message": "Notification received and processing started",
            "emailAddress": notification.emailAddress,
            "historyId": notification.historyId
        }

    except HTTPException as e:
        metrics['notifications_failed'] += 1
        raise e
    except Exception as e:
        metrics['notifications_failed'] += 1
        logger.error(f"Error handling Gmail notification: {str(e)}")
        logger.error(f"Traceback: {traceback.format_exc()}")
        raise HTTPException(status_code=500, detail=f"Internal server error: {str(e)}")

@app.post("/test-notification")
async def test_notification(test_request: TestNotificationRequest):
    """Test endpoint for simulating Gmail notifications (development only)"""
    try:
        # Only allow in development
        if os.getenv('ENVIRONMENT', 'development') != 'development':
            raise HTTPException(status_code=403, detail="Test endpoint only available in development")

        logger.info(f"Processing test notification for {test_request.emailAddress}")

        # Create notification object
        notification = GmailNotification(
            emailAddress=test_request.emailAddress,
            historyId=test_request.historyId
        )

        # Process the notification
        result = await processor.process_gmail_notification(notification)

        return {
            "test_mode": True,
            "result": result
        }

    except HTTPException as e:
        raise e
    except Exception as e:
        logger.error(f"Error in test notification: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Test failed: {str(e)}")

async def process_notification_background(notification: GmailNotification):
    """Background task to process Gmail notification"""
    try:
        logger.info(f"Starting background processing for notification: {notification.emailAddress}")
        result = await processor.process_gmail_notification(notification)

        # Update metrics based on result
        if result.get('success'):
            metrics['notifications_processed'] += 1
            metrics['emails_triggered'] += result.get('emails_processed', 0)
        else:
            metrics['notifications_failed'] += 1

        logger.info(f"Background processing completed: {result}")
    except Exception as e:
        metrics['notifications_failed'] += 1
        logger.error(f"Error in background notification processing: {str(e)}")

# Error handlers
@app.exception_handler(404)
async def not_found_handler(request: Request, exc):
    """Handle 404 errors"""
    return JSONResponse(
        status_code=404,
        content={
            "error": "Not found",
            "message": "The requested endpoint does not exist"
        }
    )

@app.exception_handler(500)
async def internal_error_handler(request: Request, exc):
    """Handle 500 errors"""
    logger.error(f"Internal server error: {str(exc)}")
    return JSONResponse(
        status_code=500,
        content={
            "error": "Internal server error",
            "message": "An unexpected error occurred"
        }
    )

if __name__ == "__main__":
    import uvicorn

    # Configuration for local development
    port = int(os.getenv('PORT', 8080))
    host = os.getenv('HOST', '0.0.0.0')
    debug = os.getenv('ENVIRONMENT', 'development') == 'development'

    logger.info(f"Starting Gmail Push Handler on {host}:{port}")
    logger.info(f"Main FastAPI URL: {MAIN_FASTAPI_URL}")
    logger.info(f"Debug mode: {debug}")

    uvicorn.run(
        "pubsub_handler:app",
        host=host,
        port=port,
        reload=debug,
        log_level="info"
    )
