# GCP Pub/Sub Setup Guide for Email Analyzer

This guide provides step-by-step instructions for setting up Google Cloud Platform (GCP) resources required for the Pub/Sub event-driven email processing architecture.

## Overview

Your email analyzer now uses a **pure event-driven architecture** with Gmail push notifications via Google Cloud Pub/Sub. This eliminates the need for scheduled processing and ensures emails are processed immediately when received.

### Architecture Flow
```
Gmail → Pub/Sub Topic → Cloud Run (pubsub_handler.py) → Backend API → Email Analysis
```

## Prerequisites

1. Google Cloud Project with billing enabled
2. Gmail API enabled
3. Cloud Run API enabled
4. Pub/Sub API enabled
5. Firestore database set up

## Step 1: Enable Required APIs

```bash
# Enable required APIs
gcloud services enable gmail.googleapis.com
gcloud services enable pubsub.googleapis.com
gcloud services enable run.googleapis.com
gcloud services enable firestore.googleapis.com
```

## Step 2: Create Pub/Sub Topic

```bash
# Create the topic for Gmail notifications
gcloud pubsub topics create email-notifications

# Verify topic creation
gcloud pubsub topics list
```

## Step 3: Create Pub/Sub Subscription (Optional for testing)

```bash
# Create a subscription for testing (optional)
gcloud pubsub subscriptions create email-notifications-sub \
    --topic=email-notifications
```

## Step 4: Set Up IAM Permissions

### Service Account for Pub/Sub Handler

```bash
# Create service account for the Pub/Sub handler
gcloud iam service-accounts create pubsub-handler \
    --display-name="Pub/Sub Email Handler"

# Grant necessary permissions
gcloud projects add-iam-policy-binding YOUR_PROJECT_ID \
    --member="serviceAccount:pubsub-handler@YOUR_PROJECT_ID.iam.gserviceaccount.com" \
    --role="roles/pubsub.subscriber"

gcloud projects add-iam-policy-binding YOUR_PROJECT_ID \
    --member="serviceAccount:pubsub-handler@YOUR_PROJECT_ID.iam.gserviceaccount.com" \
    --role="roles/datastore.user"

# Create and download service account key
gcloud iam service-accounts keys create pubsub-handler-key.json \
    --iam-account=pubsub-handler@YOUR_PROJECT_ID.iam.gserviceaccount.com
```

### Gmail API Permissions

Gmail API requires special permissions to send push notifications:

```bash
# Grant Gmail API permission to publish to your topic
gcloud pubsub topics add-iam-policy-binding email-notifications \
    --member="serviceAccount:<EMAIL>" \
    --role="roles/pubsub.publisher"
```

## Step 5: Configure Environment Variables

Create a `.env` file for your Pub/Sub handler:

```env
# GCP Configuration
GOOGLE_CLOUD_PROJECT=your-project-id
PUBSUB_TOPIC=projects/your-project-id/topics/email-notifications
GOOGLE_APPLICATION_CREDENTIALS=path/to/pubsub-handler-key.json

# Backend Configuration
MAIN_FASTAPI_URL=https://your-backend-url
SERVICE_TOKEN=your-secure-service-token

# Logging
LOG_LEVEL=INFO

# Performance Settings
MAX_RETRIES=3
REQUEST_TIMEOUT=30
MAX_EMAILS_PER_NOTIFICATION=10
```

## Step 6: Deploy Pub/Sub Handler to Cloud Run

### Build and Deploy

```bash
# Build the container
docker build -f Dockerfile.pubsub -t gcr.io/YOUR_PROJECT_ID/pubsub-handler .

# Push to Container Registry
docker push gcr.io/YOUR_PROJECT_ID/pubsub-handler

# Deploy to Cloud Run
gcloud run deploy pubsub-handler \
    --image gcr.io/YOUR_PROJECT_ID/pubsub-handler \
    --platform managed \
    --region us-central1 \
    --allow-unauthenticated \
    --set-env-vars GOOGLE_CLOUD_PROJECT=YOUR_PROJECT_ID \
    --set-env-vars PUBSUB_TOPIC=projects/YOUR_PROJECT_ID/topics/email-notifications \
    --set-env-vars MAIN_FASTAPI_URL=https://your-backend-url \
    --service-account pubsub-handler@YOUR_PROJECT_ID.iam.gserviceaccount.com
```

## Step 7: Configure Gmail Push Notifications

### Set Up Gmail Watch

Your backend includes endpoints to set up Gmail watches. Use these endpoints:

1. **Setup watches for all users:**
   ```bash
   POST /gmail/setup-watches
   ```

2. **Check watch status:**
   ```bash
   GET /gmail/watch-status
   ```

3. **Renew expiring watches:**
   ```bash
   POST /gmail/renew-watches
   ```

### Gmail API Configuration

In your Gmail API configuration, set the push notification endpoint to your Cloud Run URL:

```
Push Notification Endpoint: https://your-pubsub-handler-url/gmail-webhook
```

## Step 8: Test the Setup

### Test Pub/Sub Handler

```bash
# Test the health endpoint
curl https://your-pubsub-handler-url/health

# Test metrics endpoint
curl https://your-pubsub-handler-url/metrics
```

### Test Gmail Integration

1. Send a test email to a monitored Gmail account
2. Check the Pub/Sub handler logs
3. Verify email processing in your backend

## Step 9: Monitoring and Maintenance

### Cloud Monitoring

Set up monitoring for:
- Cloud Run service health
- Pub/Sub topic message rates
- Error rates and latency

### Watch Renewal

Gmail watches expire after 7 days. Set up a cron job or Cloud Scheduler to renew watches:

```bash
# Create Cloud Scheduler job for watch renewal
gcloud scheduler jobs create http gmail-watch-renewal \
    --schedule="0 0 * * *" \
    --uri="https://your-backend-url/gmail/renew-watches" \
    --http-method=POST \
    --headers="Authorization=Bearer YOUR_SERVICE_TOKEN"
```

## Troubleshooting

### Common Issues

1. **Pub/Sub messages not received:**
   - Check IAM <NAME_EMAIL>
   - Verify topic name matches exactly

2. **Cloud Run deployment fails:**
   - Check service account permissions
   - Verify environment variables

3. **Gmail watches not working:**
   - Check OAuth scopes include Gmail API
   - Verify push notification endpoint is accessible

### Logs and Debugging

```bash
# View Cloud Run logs
gcloud logs read --service=pubsub-handler --limit=50

# View Pub/Sub metrics
gcloud pubsub topics describe email-notifications
```

## Security Considerations

1. **Service Tokens:** Use strong, unique tokens for service-to-service communication
2. **IAM Permissions:** Follow principle of least privilege
3. **Network Security:** Consider VPC and firewall rules for production
4. **Credential Management:** Use Google Secret Manager for sensitive data

## Cost Optimization

1. **Cloud Run:** Configure appropriate CPU and memory limits
2. **Pub/Sub:** Monitor message retention and subscription settings
3. **Firestore:** Optimize queries and indexing

## Next Steps

After setup:
1. Monitor the system for 24-48 hours
2. Set up alerting for failures
3. Configure backup and disaster recovery
4. Implement additional monitoring and logging as needed

---

**Note:** Replace `YOUR_PROJECT_ID` and other placeholders with your actual values throughout this guide.
