# FastAPI Backend Requirements - Pub/Sub Event-Driven Architecture
# Core FastAPI dependencies
fastapi==0.104.1
uvicorn==0.23.2
pydantic==2.4.2
python-dotenv==1.0.0

# Google Cloud and Gmail API
google-auth-oauthlib==1.1.0
google-api-python-client==2.107.0
google-auth-httplib2==0.1.1
google-generativeai==0.8.4
firebase-admin==6.2.0

# Document processing
PyPDF2==3.0.1
python-docx==0.8.11
openpyxl==3.1.2
pillow==10.1.0

# HTTP and utilities
python-multipart==0.0.6
httpx==0.25.1
requests>=2.31.0

# Data processing
pandas

# Authentication and security
PyJWT>=2.6.0

# Email parsing
flanker

# NOTE: apscheduler removed - using Pub/Sub event-driven architecture only

