# FastAPI Pub/Sub Handler Requirements
# Dependencies for the Gmail push notification handler service
# Event-driven architecture - no scheduled processing

# FastAPI and ASGI server
fastapi>=0.104.1
uvicorn[standard]>=0.24.0

# Google Cloud dependencies
google-cloud-pubsub>=2.18.0
google-cloud-firestore>=2.12.0

# Gmail API
google-api-python-client>=2.100.0
google-auth>=2.23.0
google-auth-oauthlib>=1.1.0

# HTTP client for main FastAPI communication
requests>=2.31.0

# Data validation
pydantic>=2.5.0

# Logging and utilities
python-dotenv>=1.0.0

# Performance and monitoring
httpx>=0.25.0  # Alternative HTTP client with async support
